# 知行交易 (ZhiXing Trader)

> 计划你的交易，交易你的计划

## 项目简介

【知行交易】不是一个简单的交易记录工具，而是一个赋能交易者的"个人交易操作系统"(Personal Trading OS)。它融合了战略规划、过程控制、心理干预和数据智能，旨在帮助用户克服最大的敌人——自己的人性弱点，从而实现从随机交易到系统化交易的根本转变。

## 核心理念

- **主Slogan**: 计划你的交易，交易你的计划
- **核心价值**: 纪律比盈利更重要，成长比胜负更有价值
- **用户承诺**: 从此告别心惊胆战，让每一笔交易都成为你成长的基石

## 核心功能模块

### 1. 主仪表盘 - 交易司令部
- **个人纪律分**: 最重要的成长指标，实时反映交易纪律水平
- **核心KPI展示**: 总盈亏、胜率、平均盈亏比等关键指标
- **追踪中交易**: 当前活跃的交易计划管理
- **剧本库**: 成功交易模式的固化和复用

### 2. 新建交易计划 - 参谋部作业流程
- **交易计划质量分**: 实时评估计划完整性和可执行性
- **剧本选择**: 从预设或自定义剧本快速创建计划
- **风险管理**: 强制设置止损止盈，计算风险收益比
- **情绪与来源标记**: 记录交易心理状态和信息来源
- **纪律锁定模式**: 防止情绪化修改计划
- **决策快照**: 上传K线图作为决策依据

### 3. 交易追踪 - 纪律执行驾驶舱
- **生命周期可视化**: 直观显示当前价格在止损-止盈区间的位置
- **盘中观察日志**: 记录交易过程中的想法和情绪变化
- **实时监控**: 显示当前盈亏和关键价位
- **纪律提醒**: 锁定状态下的修改限制

### 4. 平仓复盘 - 战后复盘会议
- **纪律审视**: 评估是否严格执行了原定计划
- **交易总结**: 记录亮点、不足和经验教训
- **纪律分影响**: 实时显示对个人纪律分的影响
- **升华为剧本**: 成功交易可保存为可复用的剧本模板

### 5. 智能复盘研究院 - 个人数据科学家
- **智能洞察卡片**: AI分析生成个性化交易建议
- **情绪分析**: 统计不同情绪状态下的交易表现
- **信息源分析**: 分析不同信息来源的可靠性
- **纪律表现**: 跟踪纪律执行的改进情况

### 6. 剧本管理系统
- **系统预设剧本**: 包含"回踩多头排列"、"回踩均线"等经典模式
- **自定义剧本**: 创建和编辑个人交易模板
- **剧本表现跟踪**: 统计各剧本的成功率和收益
- **模板复用**: 快速应用成功的交易模式

### 7. 通知与提醒系统 - 场外监督员
- **价格警报**: 接近止损/止盈时的及时提醒
- **心态警报**: 检测到情绪化交易倾向时的警告
- **复盘提醒**: 督促及时完成交易复盘
- **纪律提醒**: 锁定状态和冷静期的提示

### 8. 系统设置与数据管理
- **通知设置**: 自定义提醒偏好
- **纪律设置**: 配置锁定冷却时间
- **数据导入导出**: 备份和恢复交易数据
- **系统信息**: 查看使用统计和版本信息

## 预设交易剧本

1. **回踩多头排列**: 股价在多头排列中回踩关键支撑的买入机会
2. **回踩均线**: 上升趋势中回踩重要均线的买入时机
3. **突破平台整理**: 横盘整理后向上突破的买入信号
4. **底部放量突破**: 底部区域放量突破的反转机会
5. **强势股回调**: 强势股短期回调的上车机会
6. **题材概念启动**: 基于题材概念的短期投资机会

## 快速开始

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

## 技术架构

- **框架**: Next.js 15 (React 18)
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **图标**: Lucide React
- **状态管理**: React Hooks + Local Storage

## 项目特色

1. **独特的纪律分系统**: 首创的交易纪律量化评估
2. **完整的交易生命周期管理**: 从计划到复盘的全流程覆盖
3. **智能化的洞察生成**: 基于个人数据的定制化建议
4. **丰富的预设剧本**: 涵盖主流交易策略的模板库
5. **人性化的心理干预**: 针对交易心理弱点的系统性解决方案

---

**知行交易** - 您的个人交易操作系统
> 让每一笔交易都成为您成长的基石
