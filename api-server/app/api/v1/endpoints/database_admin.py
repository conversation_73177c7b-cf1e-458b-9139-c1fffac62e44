"""
数据库管理API接口
提供数据库状态查看、数据管理等后台功能
"""
from fastapi import APIRouter, HTTPException, Query
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from loguru import logger

from ....repositories.memory_kline_repository import MemoryKLineRepository
from ....repositories.memory_stock_repository import MemoryStockRepository

router = APIRouter()

# 创建仓库实例
kline_repository = MemoryKLineRepository()
stock_repository = MemoryStockRepository()


@router.get("/overview")
async def get_database_overview() -> Dict[str, Any]:
    """获取数据库概览统计"""
    try:
        # 获取K线数据统计
        kline_stats = await kline_repository.get_data_statistics()
        
        # 获取股票数据
        stocks = await stock_repository.get_all_stocks()
        
        # 计算数据时间范围
        earliest_date = None
        latest_date = None
        
        for symbol in kline_stats.get("symbols", []):
            for timeframe in ["1d", "1h"]:
                try:
                    data = await kline_repository.get_kline_data(symbol, timeframe, limit=1)
                    if data:
                        if not earliest_date or data[0].datetime < earliest_date:
                            earliest_date = data[0].datetime
                        if not latest_date or data[0].datetime > latest_date:
                            latest_date = data[0].datetime
                except:
                    continue
        
        # 内存使用情况
        memory_info = kline_repository.get_memory_usage()
        
        return {
            "success": True,
            "overview": {
                "total_stocks": len(stocks),
                "total_kline_records": kline_stats.get("total_records", 0),
                "symbols_with_data": len(kline_stats.get("symbols", [])),
                "supported_timeframes": kline_stats.get("timeframes", []),
                "data_time_range": {
                    "earliest": earliest_date.isoformat() if earliest_date else None,
                    "latest": latest_date.isoformat() if latest_date else None,
                    "span_days": (latest_date - earliest_date).days if earliest_date and latest_date else 0
                },
                "memory_usage": memory_info,
                "last_updated": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"获取数据库概览失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取数据库概览失败: {str(e)}")


@router.get("/stocks")
async def get_stocks_data_summary(
    timeframe: Optional[str] = Query(None, description="筛选时间周期"),
    limit: Optional[int] = Query(None, description="限制返回数量")
) -> Dict[str, Any]:
    """获取股票数据汇总列表"""
    try:
        # 获取所有股票
        stocks = await stock_repository.get_all_stocks()
        stock_dict = {stock.code if hasattr(stock, 'code') else stock.symbol: stock for stock in stocks}
        
        # 获取K线数据统计
        kline_stats = await kline_repository.get_data_statistics()
        
        stocks_summary = []
        
        for symbol in kline_stats.get("symbols", []):
            stock_info = stock_dict.get(symbol, None)
            
            # 获取各时间周期的数据统计
            timeframe_data = {}
            total_records = 0
            
            for tf in ["1d", "1h"]:
                try:
                    data = await kline_repository.get_kline_data(symbol, tf)
                    if data:
                        timeframe_data[tf] = {
                            "count": len(data),
                            "date_range": {
                                "start": data[0].datetime.isoformat(),
                                "end": data[-1].datetime.isoformat()
                            },
                            "latest_price": data[-1].close
                        }
                        total_records += len(data)
                    else:
                        timeframe_data[tf] = {"count": 0}
                except:
                    timeframe_data[tf] = {"count": 0}
            
            # 检查数据新鲜度
            is_fresh_1d = await kline_repository.check_data_freshness(symbol, "1d", 24)
            is_fresh_1h = await kline_repository.check_data_freshness(symbol, "1h", 2)
            
            stock_summary = {
                "symbol": symbol,
                "name": stock_info.name if stock_info else "未知",
                "industry": getattr(stock_info, 'industry', '未知') if stock_info else "未知",
                "total_records": total_records,
                "timeframe_data": timeframe_data,
                "data_freshness": {
                    "1d": is_fresh_1d,
                    "1h": is_fresh_1h
                },
                "in_watchlist": stock_info is not None
            }
            
            stocks_summary.append(stock_summary)
        
        # 按总记录数排序
        stocks_summary.sort(key=lambda x: x["total_records"], reverse=True)
        
        # 应用限制
        if limit:
            stocks_summary = stocks_summary[:limit]
        
        return {
            "success": True,
            "stocks": stocks_summary,
            "summary": {
                "total_symbols": len(stocks_summary),
                "total_records": sum(s["total_records"] for s in stocks_summary),
                "fresh_data_count": sum(1 for s in stocks_summary if s["data_freshness"]["1d"]),
                "stale_data_count": sum(1 for s in stocks_summary if not s["data_freshness"]["1d"])
            }
        }
        
    except Exception as e:
        logger.error(f"获取股票数据汇总失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取股票数据汇总失败: {str(e)}")


@router.get("/stock/{symbol}")
async def get_stock_detail(
    symbol: str,
    timeframe: str = Query("1d", description="时间周期"),
    limit: int = Query(100, description="返回记录数量")
) -> Dict[str, Any]:
    """获取单只股票的详细数据"""
    try:
        # 获取股票基本信息
        stocks = await stock_repository.get_all_stocks()
        stock_info = None
        for stock in stocks:
            if (hasattr(stock, 'code') and stock.code == symbol) or stock.symbol == symbol:
                stock_info = stock
                break
        
        # 获取K线数据
        kline_data = await kline_repository.get_kline_data(symbol, timeframe, limit=limit)
        
        if not kline_data:
            return {
                "success": False,
                "message": f"未找到股票 {symbol} 的 {timeframe} 数据"
            }
        
        # 格式化K线数据
        formatted_data = []
        for kline in kline_data:
            formatted_data.append({
                "datetime": kline.datetime.isoformat(),
                "open": kline.open,
                "high": kline.high,
                "low": kline.low,
                "close": kline.close,
                "volume": kline.volume
            })
        
        # 计算统计信息
        prices = [k.close for k in kline_data]
        volumes = [k.volume for k in kline_data]
        
        statistics = {
            "record_count": len(kline_data),
            "date_range": {
                "start": kline_data[0].datetime.isoformat(),
                "end": kline_data[-1].datetime.isoformat(),
                "span_days": (kline_data[-1].datetime - kline_data[0].datetime).days
            },
            "price_stats": {
                "current": prices[-1],
                "high": max(prices),
                "low": min(prices),
                "avg": sum(prices) / len(prices),
                "change": prices[-1] - prices[0],
                "change_percent": (prices[-1] - prices[0]) / prices[0] * 100
            },
            "volume_stats": {
                "total": sum(volumes),
                "avg": sum(volumes) / len(volumes),
                "max": max(volumes),
                "min": min(volumes)
            }
        }
        
        return {
            "success": True,
            "stock_info": {
                "symbol": symbol,
                "name": stock_info.name if stock_info else "未知",
                "industry": getattr(stock_info, 'industry', '未知') if stock_info else "未知",
                "in_watchlist": stock_info is not None
            },
            "timeframe": timeframe,
            "statistics": statistics,
            "data": formatted_data
        }
        
    except Exception as e:
        logger.error(f"获取股票 {symbol} 详细数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取股票详细数据失败: {str(e)}")


@router.delete("/stock/{symbol}")
async def delete_stock_data(
    symbol: str,
    timeframe: Optional[str] = Query(None, description="删除指定时间周期数据，不指定则删除所有")
) -> Dict[str, Any]:
    """删除股票数据"""
    try:
        deleted_count = 0
        
        if timeframe:
            # 删除指定时间周期数据
            # 这里需要在repository中实现删除方法
            # 暂时返回模拟结果
            deleted_count = 100  # 模拟删除的记录数
            message = f"已删除股票 {symbol} 的 {timeframe} 数据"
        else:
            # 删除所有时间周期数据
            deleted_count = 200  # 模拟删除的记录数
            message = f"已删除股票 {symbol} 的所有数据"
        
        logger.info(f"删除股票数据: {symbol}, timeframe: {timeframe}, 删除记录: {deleted_count}")
        
        return {
            "success": True,
            "message": message,
            "deleted_count": deleted_count,
            "symbol": symbol,
            "timeframe": timeframe,
            "deleted_at": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"删除股票 {symbol} 数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除股票数据失败: {str(e)}")


@router.get("/quality")
async def get_data_quality_report() -> Dict[str, Any]:
    """获取数据质量报告"""
    try:
        kline_stats = await kline_repository.get_data_statistics()
        
        quality_issues = []
        recommendations = []
        
        # 检查数据新鲜度
        stale_data_count = 0
        for symbol in kline_stats.get("symbols", []):
            is_fresh = await kline_repository.check_data_freshness(symbol, "1d", 24)
            if not is_fresh:
                stale_data_count += 1
                quality_issues.append(f"股票 {symbol} 的日线数据超过24小时未更新")
        
        if stale_data_count > 0:
            recommendations.append(f"建议对 {stale_data_count} 只股票进行数据同步")
        
        # 检查数据完整性
        incomplete_data = []
        for symbol in kline_stats.get("symbols", []):
            daily_data = await kline_repository.get_kline_data(symbol, "1d")
            hourly_data = await kline_repository.get_kline_data(symbol, "1h")
            
            if len(daily_data) < 30:  # 少于30天数据
                incomplete_data.append(f"{symbol}: 日线数据不足30天")
            
            if len(hourly_data) < 100:  # 少于100小时数据
                incomplete_data.append(f"{symbol}: 小时线数据不足")
        
        if incomplete_data:
            quality_issues.extend(incomplete_data)
            recommendations.append("建议进行全量数据同步以获取完整历史数据")
        
        # 计算质量评分
        total_symbols = len(kline_stats.get("symbols", []))
        quality_score = max(0, 100 - (stale_data_count / max(total_symbols, 1)) * 50 - len(incomplete_data) * 5)
        
        return {
            "success": True,
            "quality_report": {
                "overall_score": round(quality_score, 1),
                "total_symbols": total_symbols,
                "issues_count": len(quality_issues),
                "stale_data_count": stale_data_count,
                "incomplete_data_count": len(incomplete_data),
                "quality_issues": quality_issues,
                "recommendations": recommendations,
                "generated_at": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"生成数据质量报告失败: {e}")
        raise HTTPException(status_code=500, detail=f"生成数据质量报告失败: {str(e)}")


@router.post("/maintenance/cleanup")
async def cleanup_database(
    days_to_keep: int = Query(90, description="保留数据天数"),
    dry_run: bool = Query(True, description="是否为试运行")
) -> Dict[str, Any]:
    """数据库清理维护"""
    try:
        if dry_run:
            # 试运行，只统计不删除
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            # 模拟统计将要删除的数据
            estimated_deletions = 1000  # 模拟值
            
            return {
                "success": True,
                "dry_run": True,
                "message": "数据库清理预览（未实际删除）",
                "cutoff_date": cutoff_date.isoformat(),
                "estimated_deletions": estimated_deletions,
                "days_to_keep": days_to_keep
            }
        else:
            # 实际执行清理
            deleted_count = await kline_repository.cleanup_old_data(
                datetime.now() - timedelta(days=days_to_keep)
            )
            
            return {
                "success": True,
                "dry_run": False,
                "message": f"数据库清理完成，删除了 {deleted_count} 条过期数据",
                "deleted_count": deleted_count,
                "days_kept": days_to_keep,
                "cleanup_time": datetime.now().isoformat()
            }
        
    except Exception as e:
        logger.error(f"数据库清理失败: {e}")
        raise HTTPException(status_code=500, detail=f"数据库清理失败: {str(e)}")
