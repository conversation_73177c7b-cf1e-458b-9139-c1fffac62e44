[tool:pytest]
# pytest配置文件

# 测试目录
testpaths = tests

# 测试文件模式
python_files = test_*.py

# 测试类模式
python_classes = Test*

# 测试函数模式
python_functions = test_*

# 输出选项
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes

# 标记定义
markers =
    slow: 标记为慢速测试
    integration: 标记为集成测试
    unit: 标记为单元测试
    yahoo: 标记为Yahoo Finance相关测试
    data: 标记为数据相关测试

# 异步测试支持
asyncio_mode = auto

# 最小Python版本
minversion = 6.0
