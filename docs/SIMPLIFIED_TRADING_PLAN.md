# 📈 简化版交易计划使用指南

## 🎯 设计理念

根据您的实际需求，我们将交易计划表单简化为核心要素：
1. **技术走势图截图** - 保存您在券商软件上的划线分析
2. **买入理由** - 简单记录为什么现在买入
3. **价格设置** - 买入价、止损、止盈
4. **移动止盈** - 灵活的盈利管理策略

## 📝 简化后的表单结构

### 1. 股票信息
- **股票代码**：如 000001
- **股票名称**：如 平安银行

### 2. 价格设置
- **计划买入价**：您准备买入的价格
- **止损价**：风险控制价位
- **止盈价**：目标盈利价位
- **移动止盈**：可选择启用动态止盈

### 3. 买入理由
- **简单描述**：为什么现在买入这只股票？
  - 例如："突破关键阻力位，成交量放大"
  - 例如："回踩20日均线获得支撑，MACD金叉"

### 4. 决策快照
- **K线图截图**：保存您的技术分析图表
- **支持粘贴**：直接 Ctrl+V 粘贴截图

## 🚀 快速使用流程

### 步骤1：在券商软件中分析
1. 打开您的券商交易软件
2. 查看目标股票的K线图
3. 使用画线工具标记关键位置（支撑、阻力、趋势线等）
4. 截取这个分析图表

### 步骤2：创建交易计划
1. 访问 http://localhost:3000
2. 点击"新建交易计划"
3. 填写股票基本信息
4. 设置买入价、止损价、止盈价
5. 点击决策快照区域，按 Ctrl+V 粘贴截图
6. 简单描述买入理由

### 步骤3：提交计划
- 只需填写必填字段即可提交
- 不再需要80分质量分要求
- 专注于核心交易决策

## 🔧 移动止盈功能

### 什么是移动止盈？
- 当股价上涨时，自动调整止盈价位
- 保护已获得的利润
- 让利润奔跑，同时控制回撤

### 如何使用？
1. 在价格设置中勾选"启用移动止盈"
2. 设置初始止盈价
3. 系统会在盈利后动态调整止盈位

## 📸 截图粘贴技巧

### 推荐的截图工具
- **Windows**：Win + Shift + S（系统自带）
- **macOS**：Cmd + Shift + 4
- **第三方工具**：Snipaste、QQ截图等

### 粘贴步骤
1. 在券商软件中截图
2. 回到交易计划页面
3. 点击决策快照的蓝色虚线框
4. 按 Ctrl+V（Windows）或 Cmd+V（macOS）

### 故障排除
- 如果粘贴不成功，按 F12 查看控制台日志
- 确保点击了上传区域获得焦点
- 可以使用拖拽或点击上传作为备选方案

## 💡 实际使用示例

### 示例1：突破买入
```
股票：平安银行 (000001)
买入价：12.50
止损价：11.80
止盈价：14.00
买入理由：突破12.30阻力位，成交量放大，MACD金叉确认
```

### 示例2：回踩买入
```
股票：招商银行 (600036)
买入价：42.80
止损价：40.50
止盈价：46.50
移动止盈：启用
买入理由：回踩20日均线获得支撑，RSI超卖反弹
```

## 🎨 界面优化

### 简化的好处
- ✅ 填写时间从10分钟缩短到2分钟
- ✅ 专注核心决策要素
- ✅ 减少不必要的复杂字段
- ✅ 更符合实际交易习惯

### 保留的核心功能
- ✅ 技术图表保存
- ✅ 风险收益比计算
- ✅ 移动止盈管理
- ✅ 交易记录追踪

## 📊 后续功能

简化后的计划仍然支持：
- 交易追踪和执行
- 复盘分析
- 统计报告
- 纪律分评估

## 🔄 从复杂到简单

### 移除的字段
- ❌ 复杂的情绪选择
- ❌ 信息来源分类
- ❌ 基本面分析（可选在买入理由中提及）
- ❌ 消息面分析
- ❌ 交易剧本选择（简化为直接填写）
- ❌ 纪律锁定（简化流程）

### 保留的核心
- ✅ 股票信息
- ✅ 价格设置
- ✅ 买入理由
- ✅ 技术图表
- ✅ 风险管理

现在您可以更快速、更专注地创建交易计划，把更多时间用在市场分析上！🚀
